import logging
import re
import time
from pathlib import Path

# https://github.com/docpip installling-project/docling
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.settings import settings
from docling.datamodel.pipeline_options import (
    AcceleratorDevice,
    AcceleratorOptions,
    PdfPipelineOptions,
    EasyOcrOptions,
    TesseractOcrOptions,
)

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    BarColumn,
    TaskProgressColumn,
    TimeElapsedColumn,
    TextColumn,
)
from rich.panel import Panel
from rich.text import Text
from rich.table import Table

# Configuration de la console Rich
console = Console()

_log = logging.getLogger(__name__)


def print_header(title: str, subtitle: str = ""):
    """Affiche un en-tête stylisé avec Rich"""
    header_text = f"[bold blue]{title}[/bold blue]"
    if subtitle:
        header_text += f"\n[dim]{subtitle}[/dim]"

    console.print(Panel.fit(header_text, border_style="blue"))


def print_success(message: str):
    """Affiche un message de succès"""
    console.print(f"✅ [bold green]{message}[/bold green]")


def print_info(message: str):
    """Affiche un message d'information"""
    console.print(f"ℹ️  [cyan]{message}[/cyan]")


def print_warning(message: str):
    """Affiche un message d'avertissement"""
    console.print(f"⚠️  [bold yellow]{message}[/bold yellow]")


def print_error(message: str):
    """Affiche un message d'erreur"""
    console.print(f"❌ [bold red]{message}[/bold red]")


def print_processing_time(description: str, time_seconds: float):
    """Affiche le temps de traitement formaté"""
    console.print(f"⏱️  [dim]{description}: {time_seconds:.2f}s[/dim]")


def print_file_saved(file_path: str):
    """Affiche un message de fichier sauvegardé"""
    console.print(f"💾 [bold green]Fichier sauvegardé:[/bold green] [link]{file_path}[/link]")


def create_processing_progress(total_items: int, description: str = "Traitement en cours..."):
    """Crée une barre de progression pour le traitement"""
    return Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    )


def unstructured_trombinoscope():
    # pip install unstructured[local-inference]
    # sudo apt install tesseract-ocr-all
    # pip install pdfminer-six ?
    from unstructured.partition.pdf import partition_pdf
    from unstructured.staging.base import elements_to_json

    ## include_page_breaks
    # include page breaks (default is False)
    include_page_breaks = True

    ## strategy
    # The strategy to use for partitioning the PDF. Valid strategies are "hi_res", "ocr_only", and "fast".
    # When using the "hi_res" strategy, the function uses a layout detection model to identify document elements.
    # hi_res" is used for analyzing PDFs and extracting table structure (default is "auto")
    strategy = "hi_res"

    ## infer_table_structure
    # Only applicable if `strategy=hi_res`.
    # If True, any Table elements that are extracted will also have a metadata field named "text_as_html" where the table's text content is rendered into an html string.
    # I.e., rows and cells are preserved.
    # Whether True or False, the "text" field is always present in any Table element and is the text content of the table (no structure).

    if strategy == "hi_res":
        infer_table_structure = True
    else:
        infer_table_structure = False

    ## extract_element_types
    # Get images of tables
    if infer_table_structure:
        extract_element_types = ["Table"]
    else:
        extract_element_types = None

    ## max_characters
    # The maximum number of characters to include in a partition (document element)
    # If None is passed, no maximum is applied.
    # Only applies to the "ocr_only" strategy (default is 1500)
    max_characters = 1500
    if strategy != "ocr_only":
        max_characters = None

    ## languages
    # The languages to use for the Tesseract agent.
    # To use a language, you'll first need to install the appropriate Tesseract language pack.
    languages = ["fra"]  # example if more than one "eng+por" (default is "eng")

    ## model_name
    # @requires_dependencies("unstructured_inference")
    # yolox: best model for table extraction. Other options are yolox_quantized, detectron2_onnx and chipper depending on file layout
    # source: https://docs.unstructured.io/open-source/concepts/models
    hi_res_model_name = "yolox"

    unstructured_elements = partition_pdf(
        filename="data/trombinoscope.pdf",
        include_page_breaks=include_page_breaks,
        strategy=strategy,
        infer_table_structure=infer_table_structure,
        extract_element_types=extract_element_types,
        max_characters=max_characters,
        languages=languages,
        hi_res_model_name=hi_res_model_name,
    )

    elements_to_json(unstructured_elements, filename="unstructured_trombinoscope.json")

def unstructured_to_markdown(unstructured_elements, output_path):
    """
    TODO
    Convert unstructured elements to markdown format and save to a file.

    Args:
        unstructured_elements: List of elements from unstructured's partition_pdf
        output_path (str): Path to save the markdown file
    """
    with open(output_path, 'w', encoding='utf-8') as f:
        for element in unstructured_elements:
            # Handle different element types
            if element.category == "PageBreak":
                f.write("\n\n---\n\n")  # Page break as horizontal rule

            elif element.category == "Title":
                f.write(f"# {element.text}\n\n")

            elif element.category == "NarrativeText":
                f.write(f"{element.text}\n\n")

            elif element.category == "ListItem":
                f.write(f"- {element.text}\n")

            elif element.category == "Table":
                # If we have HTML structured table representation
                if hasattr(element, 'metadata') and element.metadata and 'text_as_html' in element.metadata:
                    # Convert HTML table to markdown format
                    html_table = element.metadata['text_as_html']
                    # Basic HTML to markdown conversion for tables
                    # This is simplified and might need adjustments for complex tables
                    md_table = html_table.replace('<table>', '').replace('</table>', '')
                    md_table = md_table.replace('<tr>', '').replace('</tr>', '\n')
                    md_table = md_table.replace('<th>', '| ').replace('</th>', ' ')
                    md_table = md_table.replace('<td>', '| ').replace('</td>', ' ')

                    # Add trailing pipe to each row and split into lines
                    lines = [line + '|' for line in md_table.strip().split('\n') if line.strip()]

                    if lines:
                        # Add header separator after first row
                        if '<th>' in html_table:
                            # Count pipes to determine number of columns
                            col_count = lines[0].count('|') - 1
                            lines.insert(1, '|' + '---|' * col_count)

                        f.write('\n'.join(lines) + '\n\n')
                else:
                    # Fallback to plain text if structured format not available
                    f.write(f"```\n{element.text}\n```\n\n")

            elif element.category == "Image" or element.category == "Figure":
                # If image or figure has a caption in metadata
                caption = ""
                if hasattr(element, 'metadata') and element.metadata and 'caption' in element.metadata:
                    caption = element.metadata['caption']

                # Note: actual image embedding would require handling the binary data
                # Here we just indicate there was an image
                f.write(f"![{caption}](Image from document)\n\n")

            elif element.category == "Header":
                f.write(f"## {element.text}\n\n")

            elif element.category == "Footer":
                f.write(f"*{element.text}*\n\n")

            elif element.category == "UncategorizedText":
                f.write(f"{element.text}\n\n")
            else:
                f.write(f"{element.text}\n\n")

    print_file_saved(output_path)


    import json

    with open("unstructured_trombinoscope.json", "r") as file:
        data = json.load(file)

    extracted_elements = []
    for entry in data:
        if entry["type"] == "Title":
            text = "<h1>" + entry["text"] + "</h1>"
        elif entry["type"] == "Table":
            text = entry["metadata"]["text_as_html"]
        else:
            text = "<p>" + entry["text"] + "</p>"

        extracted_elements.append(text)

def main():
    logging.basicConfig(level=logging.INFO)

    # Affichage de l'en-tête
    print_header("🔍 PDF2MD - Conversion PDF vers Markdown", "Utilisation de Docling avec EasyOCR")

    input_doc_path = Path("data/trombinoscope.pdf")

    if not input_doc_path.exists():
        print_error(f"Le fichier PDF '{input_doc_path}' n'existe pas!")
        return

    print_info(f"📄 Fichier d'entrée: {input_doc_path}")
    print_info(f"📁 Répertoire de travail: {Path.cwd()}")

    # Docling Parse with EasyOCR
    # https://docling-project.github.io/docling/examples/custom_convert/
    pipeline_options = PdfPipelineOptions()
    pipeline_options.do_ocr = True
    pipeline_options.do_table_structure = True
    pipeline_options.table_structure_options.do_cell_matching = True
    # Any of the OCR options can be used:EasyOcrOptions, TesseractOcrOptions, TesseractCliOcrOptions, OcrMacOptions(Mac only), RapidOcrOptions
    # https://docling-project.github.io/docling/installation/
    # https://github.com/straussmaximilian/ocrmac
    # OcrMacOptions(force_full_page_ocr=True)
    # https://github.com/RapidAI/RapidOCR
    # RapidOcrOptions(force_full_page_ocr=True)

    # https://tesseract-ocr.github.io/tessdoc/
    # TesseractCliOcrOptions(force_full_page_ocr=True)
    # sudo apt install tesseract-ocr-all
    #os.environ["TESSDATA_PREFIX"] = "/usr/share/tesseract-ocr/5/tessdata"
    #pipeline_options.ocr_options = TesseractOcrOptions(force_full_page_ocr=True)
    #pipeline_options.ocr_options.lang = ["fra"]

    # https://www.jaided.ai/easyocr/
    pipeline_options.ocr_options = EasyOcrOptions(force_full_page_ocr=True)
    pipeline_options.ocr_options.lang = ["fr"]

    pipeline_options.accelerator_options = AcceleratorOptions(
        num_threads=8,
        device=AcceleratorDevice.AUTO,
        cuda_use_flash_attention2=True,
    )

    # Affichage de la configuration
    config_table = Table(title="Configuration de traitement")
    config_table.add_column("Paramètre", style="cyan")
    config_table.add_column("Valeur", style="green")

    config_table.add_row("OCR", "EasyOCR")
    config_table.add_row("Langue", "Français")
    config_table.add_row("Threads", "8")
    config_table.add_row("Device", "AUTO")
    config_table.add_row("OCR complet", "Activé")
    config_table.add_row("Structure des tableaux", "Activé")

    console.print(config_table)

    doc_converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
        }
    )

    # Enable the profiling to measure the time spent
    settings.debug.profile_pipeline_timings = True

    print_info("🚀 Démarrage de la conversion...")
    start_time = time.time()

    with console.status("[bold green]Conversion en cours...", spinner="dots"):
        conversion_result = doc_converter.convert(input_doc_path)

    conversion_time = time.time() - start_time
    print_processing_time("Temps de conversion", conversion_time)

    # Affichage des statistiques de traitement détaillées
    if conversion_result.timings:
        print_info("📊 Statistiques de traitement détaillées:")

        timing_table = Table(title="Temps de traitement par étape")
        timing_table.add_column("Étape", style="cyan")
        timing_table.add_column("Scope", style="yellow")
        timing_table.add_column("Nombre", style="green")
        timing_table.add_column("Temps moyen (s)", style="magenta")
        timing_table.add_column("Temps total (s)", style="red")
        timing_table.add_column("Écart-type (s)", style="dim")

        for key, timing_info in conversion_result.timings.items():
            if timing_info.times:
                avg_time = timing_info.avg()
                total_time_step = sum(timing_info.times)
                std_time = timing_info.std()

                timing_table.add_row(
                    key,
                    timing_info.scope.value,
                    str(timing_info.count),
                    f"{avg_time:.3f}",
                    f"{total_time_step:.3f}",
                    f"{std_time:.3f}"
                )

        console.print(timing_table)

        # Statistiques par page si disponibles
        page_timings = {k: v for k, v in conversion_result.timings.items()
                       if v.scope.value == "page" and v.times}

        if page_timings:
            print_info("📄 Statistiques par page:")
            for key, timing_info in page_timings.items():
                if timing_info.times:
                    avg_time = timing_info.avg()
                    min_time = min(timing_info.times)
                    max_time = max(timing_info.times)
                    console.print(
                        f"  • [cyan]{key}[/cyan]: "
                        f"[green]{timing_info.count} pages[/green] | "
                        f"[yellow]Moy: {avg_time:.3f}s[/yellow] | "
                        f"[red]Min: {min_time:.3f}s[/red] | "
                        f"[magenta]Max: {max_time:.3f}s[/magenta]"
                    )

    print_info("📝 Génération du fichier Markdown...")
    data_md = conversion_result.document.export_to_markdown()
    data_md = re.sub(r'<!-- image -->', '', data_md)
    data_md = re.sub(r'/gid\S+', '', data_md)

    output_file = "docling_trombinoscope.md"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(data_md)

    print_file_saved(output_file)

    # Affichage du résumé final
    total_time = time.time() - start_time
    num_pages = len(conversion_result.document.pages) if conversion_result.document.pages else 0
    avg_time_per_page = total_time / num_pages if num_pages > 0 else 0

    console.print(
        Panel.fit(
            f"[bold green]🎉 Conversion terminée avec succès![/bold green]\n"
            f"📄 Fichier de sortie: [cyan]{output_file}[/cyan]\n"
            f"📊 Nombre de pages: [yellow]{num_pages}[/yellow]\n"
            f"⏱️  Temps total: [yellow]{total_time:.2f}s[/yellow]\n"
            f"📈 Temps moyen par page: [yellow]{avg_time_per_page:.2f}s[/yellow]\n"
            f"📝 Taille du fichier: [yellow]{len(data_md)} caractères[/yellow]",
            border_style="green",
        )
    )

    # from docling_core.transforms.chunker import HierarchicalChunker
    # from docling.chunking import HybridChunker

    # Hierarchical chunking
    # chunker = HierarchicalChunker()
    # texts = [chunk.text for chunk in chunker.chunk(conversion_result.document)]
    # with open("docling_trombinoscope.txt", mode="w", encoding="utf-8") as f:
    #    for text_item in texts:
    #        f.write(text_item + "\n")

    # Hybrid chunking
    # https://docling-project.github.io/docling/concepts/chunking/
    # https://docling-project.github.io/docling/examples/hybrid_chunking/
    # chunker = HybridChunker()
    # chunk_iter = chunker.chunk(dl_doc=conversion_result.document)
    # with open("docling_trombinoscope.txt", mode="w", encoding="utf-8") as f:
    #    for chunk in chunk_iter:
    #        f.write(chunk.text + "\n")


if __name__ == "__main__":
    main()
